/* ===== VENDOR DASHBOARD PAGE CSS ===== */

/* === WELCOME SECTION === */
.dashboard-welcome {
    background: linear-gradient(135deg, var(--vendor-brand-primary), var(--vendor-brand-primary-700));
    border-radius: var(--vendor-radius-2xl);
    padding: var(--vendor-space-8);
    margin-bottom: var(--vendor-space-8);
    color: var(--vendor-white);
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(126, 217, 87, 0.3);
}

.dashboard-welcome::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.dashboard-welcome::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 150px;
    height: 150px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    transform: translate(-50%, 50%);
}

.welcome-content {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--vendor-space-8);
}

@media (max-width: 768px) {
    .welcome-content {
        flex-direction: column;
        text-align: center;
        gap: var(--vendor-space-6);
    }
}

.welcome-text {
    flex: 1;
}

.welcome-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--vendor-space-2);
    background: rgba(255, 255, 255, 0.2);
    color: var(--vendor-white);
    padding: var(--vendor-space-2) var(--vendor-space-4);
    border-radius: var(--vendor-radius-full);
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    margin-bottom: var(--vendor-space-4);
    backdrop-filter: blur(10px);
}

.welcome-title {
    font-size: var(--vendor-text-3xl);
    font-weight: var(--vendor-font-bold);
    margin-bottom: var(--vendor-space-3);
    line-height: var(--vendor-leading-tight);
}

@media (max-width: 768px) {
    .welcome-title {
        font-size: var(--vendor-text-2xl);
    }
}

.welcome-subtitle {
    font-size: var(--vendor-text-lg);
    opacity: 0.9;
    line-height: var(--vendor-leading-relaxed);
}

.welcome-actions {
    display: flex;
    gap: var(--vendor-space-3);
    margin-top: var(--vendor-space-6);
}

@media (max-width: 640px) {
    .welcome-actions {
        flex-direction: column;
        width: 100%;
    }
}

.welcome-btn {
    background: rgba(255, 255, 255, 0.2);
    color: var(--vendor-white);
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: var(--vendor-space-3) var(--vendor-space-6);
    border-radius: var(--vendor-radius-lg);
    text-decoration: none;
    font-weight: var(--vendor-font-medium);
    transition: var(--vendor-transition-fast);
    backdrop-filter: blur(10px);
    display: inline-flex;
    align-items: center;
    gap: var(--vendor-space-2);
}

.welcome-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.welcome-btn.primary {
    background: var(--vendor-white);
    color: var(--vendor-brand-primary);
    border-color: var(--vendor-white);
}

.welcome-btn.primary:hover {
    background: var(--vendor-gray-100);
    color: var(--vendor-brand-primary-600);
}

.welcome-illustration {
    flex-shrink: 0;
    width: 200px;
    height: 150px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--vendor-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

@media (max-width: 768px) {
    .welcome-illustration {
        width: 150px;
        height: 100px;
        font-size: 3rem;
    }
}

/* === STATS GRID === */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-8);
}

@media (max-width: 640px) {
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-4);
    }
}

.stat-card {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    padding: var(--vendor-space-6);
    transition: var(--vendor-transition-fast);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--vendor-shadow-lg);
    border-color: var(--vendor-border-accent);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--vendor-brand-primary), var(--vendor-brand-primary-400));
    border-radius: var(--vendor-radius-xl) var(--vendor-radius-xl) 0 0;
}

.stat-card.success::before {
    background: linear-gradient(90deg, var(--vendor-success), var(--vendor-success-light));
}

.stat-card.warning::before {
    background: linear-gradient(90deg, var(--vendor-warning), var(--vendor-warning-light));
}

.stat-card.error::before {
    background: linear-gradient(90deg, var(--vendor-error), var(--vendor-error-light));
}

.stat-card.info::before {
    background: linear-gradient(90deg, var(--vendor-info), var(--vendor-info-light));
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--vendor-space-4);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--vendor-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-text-xl);
    color: var(--vendor-white);
    background: var(--vendor-brand-primary);
}

.stat-card.success .stat-icon {
    background: var(--vendor-success);
}

.stat-card.warning .stat-icon {
    background: var(--vendor-warning);
}

.stat-card.error .stat-icon {
    background: var(--vendor-error);
}

.stat-card.info .stat-icon {
    background: var(--vendor-info);
}

.stat-menu {
    background: none;
    border: none;
    color: var(--vendor-text-tertiary);
    cursor: pointer;
    padding: var(--vendor-space-1);
    border-radius: var(--vendor-radius-base);
    transition: var(--vendor-transition-fast);
}

.stat-menu:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-primary);
}

.stat-content {
    margin-bottom: var(--vendor-space-4);
}

.stat-label {
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-tertiary);
    font-weight: var(--vendor-font-medium);
    margin-bottom: var(--vendor-space-1);
}

.stat-value {
    font-size: var(--vendor-text-3xl);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
    line-height: var(--vendor-leading-none);
    margin-bottom: var(--vendor-space-2);
}

.stat-change {
    display: inline-flex;
    align-items: center;
    gap: var(--vendor-space-1);
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    padding: var(--vendor-space-1) var(--vendor-space-2);
    border-radius: var(--vendor-radius-base);
}

.stat-change.positive {
    background: var(--vendor-success-50);
    color: var(--vendor-success-700);
}

.stat-change.negative {
    background: var(--vendor-error-50);
    color: var(--vendor-error-700);
}

.stat-change.neutral {
    background: var(--vendor-gray-100);
    color: var(--vendor-gray-700);
}

.dark .stat-change.positive {
    background: rgba(16, 185, 129, 0.1);
    color: var(--vendor-success-light);
}

.dark .stat-change.negative {
    background: rgba(239, 68, 68, 0.1);
    color: var(--vendor-error-light);
}

.dark .stat-change.neutral {
    background: var(--vendor-gray-800);
    color: var(--vendor-gray-300);
}

.stat-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: var(--vendor-space-4);
    border-top: 1px solid var(--vendor-border-primary);
}

.stat-period {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
}

.stat-link {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-brand-primary);
    text-decoration: none;
    font-weight: var(--vendor-font-medium);
    transition: var(--vendor-transition-fast);
}

.stat-link:hover {
    color: var(--vendor-brand-primary-600);
}

/* === CHART SECTION === */
.chart-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-8);
}

@media (max-width: 1024px) {
    .chart-section {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-4);
    }
}

.chart-card {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    overflow: hidden;
}

.chart-header {
    padding: var(--vendor-space-6);
    border-bottom: 1px solid var(--vendor-border-primary);
    background: var(--vendor-surface-secondary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chart-title {
    font-size: var(--vendor-text-lg);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin: 0;
}

.chart-controls {
    display: flex;
    gap: var(--vendor-space-2);
}

.chart-control {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    color: var(--vendor-text-secondary);
    padding: var(--vendor-space-2) var(--vendor-space-3);
    border-radius: var(--vendor-radius-base);
    font-size: var(--vendor-text-xs);
    cursor: pointer;
    transition: var(--vendor-transition-fast);
}

.chart-control:hover,
.chart-control.active {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    border-color: var(--vendor-brand-primary);
}

.chart-body {
    padding: var(--vendor-space-6);
    min-height: 300px;
}

/* === RECENT ACTIVITY === */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: var(--vendor-space-4);
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: var(--vendor-space-4);
    padding: var(--vendor-space-4);
    border-radius: var(--vendor-radius-lg);
    transition: var(--vendor-transition-fast);
}

.activity-item:hover {
    background: var(--vendor-surface-hover);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--vendor-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-text-sm);
    color: var(--vendor-white);
    background: var(--vendor-brand-primary);
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-title {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-1);
}

.activity-description {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    line-height: var(--vendor-leading-relaxed);
}

.activity-time {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    white-space: nowrap;
}

/* === QUICK ACTIONS === */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--vendor-space-4);
    margin-bottom: var(--vendor-space-8);
}

.quick-action {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-lg);
    padding: var(--vendor-space-5);
    text-decoration: none;
    color: var(--vendor-text-primary);
    transition: var(--vendor-transition-fast);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: var(--vendor-space-3);
}

.quick-action:hover {
    transform: translateY(-2px);
    box-shadow: var(--vendor-shadow-md);
    border-color: var(--vendor-brand-primary);
}

.quick-action-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--vendor-radius-lg);
    background: var(--vendor-bg-accent);
    color: var(--vendor-brand-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-text-xl);
}

.quick-action-title {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-semibold);
    margin: 0;
}

.quick-action-description {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    margin: 0;
}
