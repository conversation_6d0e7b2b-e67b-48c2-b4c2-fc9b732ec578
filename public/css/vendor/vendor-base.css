/* ===== VENDOR BASE CSS - DESIGN SYSTEM ===== */

/* CSS Custom Properties - Design Tokens */
:root {
    /* === BRAND COLORS - WhaMart Green Theme === */
    --vendor-brand-primary: #7ED957;
    --vendor-brand-primary-50: #F8FFF5;
    --vendor-brand-primary-100: #E8FFE1;
    --vendor-brand-primary-200: #D1FFB8;
    --vendor-brand-primary-300: #B8FF8F;
    --vendor-brand-primary-400: #9FFF66;
    --vendor-brand-primary-500: #7ED957;
    --vendor-brand-primary-600: #5DC264;
    --vendor-brand-primary-700: #4CAF50;
    --vendor-brand-primary-800: #3EA045;
    --vendor-brand-primary-900: #2E7D32;
    
    /* === SEMANTIC COLORS === */
    --vendor-success: #4CAF50;
    --vendor-success-light: #66BB6A;
    --vendor-success-dark: #388E3C;
    --vendor-success-50: #E8F5E8;
    --vendor-success-100: #C8E6C9;
    --vendor-success-500: #4CAF50;
    --vendor-success-600: #43A047;
    --vendor-success-700: #388E3C;
    
    --vendor-warning: #f59e0b;
    --vendor-warning-light: #fbbf24;
    --vendor-warning-dark: #d97706;
    --vendor-warning-50: #fffbeb;
    --vendor-warning-100: #fef3c7;
    --vendor-warning-500: #f59e0b;
    --vendor-warning-600: #d97706;
    --vendor-warning-700: #b45309;
    
    --vendor-error: #ef4444;
    --vendor-error-light: #f87171;
    --vendor-error-dark: #dc2626;
    --vendor-error-50: #fef2f2;
    --vendor-error-100: #fee2e2;
    --vendor-error-500: #ef4444;
    --vendor-error-600: #dc2626;
    --vendor-error-700: #b91c1c;
    
    --vendor-info: #3b82f6;
    --vendor-info-light: #60a5fa;
    --vendor-info-dark: #2563eb;
    --vendor-info-50: #eff6ff;
    --vendor-info-100: #dbeafe;
    --vendor-info-500: #3b82f6;
    --vendor-info-600: #2563eb;
    --vendor-info-700: #1d4ed8;
    
    /* === NEUTRAL COLORS === */
    --vendor-white: #ffffff;
    --vendor-black: #000000;
    --vendor-gray-50: #f9fafb;
    --vendor-gray-100: #f3f4f6;
    --vendor-gray-200: #e5e7eb;
    --vendor-gray-300: #d1d5db;
    --vendor-gray-400: #9ca3af;
    --vendor-gray-500: #6b7280;
    --vendor-gray-600: #4b5563;
    --vendor-gray-700: #374151;
    --vendor-gray-800: #1f2937;
    --vendor-gray-900: #111827;
    
    /* === LAYOUT VARIABLES === */
    --vendor-sidebar-width: 280px;
    --vendor-sidebar-collapsed-width: 80px;
    --vendor-header-height: 80px;
    --vendor-mobile-header-height: 64px;
    --vendor-content-max-width: 1400px;
    --vendor-container-padding: 2rem;
    --vendor-container-padding-mobile: 1rem;
    
    /* === SPACING SCALE === */
    --vendor-space-0: 0;
    --vendor-space-1: 0.25rem;   /* 4px */
    --vendor-space-2: 0.5rem;    /* 8px */
    --vendor-space-3: 0.75rem;   /* 12px */
    --vendor-space-4: 1rem;      /* 16px */
    --vendor-space-5: 1.25rem;   /* 20px */
    --vendor-space-6: 1.5rem;    /* 24px */
    --vendor-space-8: 2rem;      /* 32px */
    --vendor-space-10: 2.5rem;   /* 40px */
    --vendor-space-12: 3rem;     /* 48px */
    --vendor-space-16: 4rem;     /* 64px */
    --vendor-space-20: 5rem;     /* 80px */
    --vendor-space-24: 6rem;     /* 96px */
    
    /* === BORDER RADIUS === */
    --vendor-radius-none: 0;
    --vendor-radius-sm: 0.375rem;   /* 6px */
    --vendor-radius-base: 0.5rem;   /* 8px */
    --vendor-radius-md: 0.75rem;    /* 12px */
    --vendor-radius-lg: 1rem;       /* 16px */
    --vendor-radius-xl: 1.25rem;    /* 20px */
    --vendor-radius-2xl: 1.5rem;    /* 24px */
    --vendor-radius-3xl: 2rem;      /* 32px */
    --vendor-radius-full: 9999px;
    
    /* === SHADOWS === */
    --vendor-shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --vendor-shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --vendor-shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --vendor-shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --vendor-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --vendor-shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --vendor-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --vendor-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    
    /* === TRANSITIONS === */
    --vendor-transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --vendor-transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --vendor-transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --vendor-transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    
    /* === TYPOGRAPHY === */
    --vendor-font-family-sans: 'Inter', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --vendor-font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    
    /* Font Sizes */
    --vendor-text-xs: 0.75rem;      /* 12px */
    --vendor-text-sm: 0.875rem;     /* 14px */
    --vendor-text-base: 1rem;       /* 16px */
    --vendor-text-lg: 1.125rem;     /* 18px */
    --vendor-text-xl: 1.25rem;      /* 20px */
    --vendor-text-2xl: 1.5rem;      /* 24px */
    --vendor-text-3xl: 1.875rem;    /* 30px */
    --vendor-text-4xl: 2.25rem;     /* 36px */
    --vendor-text-5xl: 3rem;        /* 48px */
    --vendor-text-6xl: 3.75rem;     /* 60px */
    
    /* Font Weights */
    --vendor-font-thin: 100;
    --vendor-font-light: 300;
    --vendor-font-normal: 400;
    --vendor-font-medium: 500;
    --vendor-font-semibold: 600;
    --vendor-font-bold: 700;
    --vendor-font-extrabold: 800;
    --vendor-font-black: 900;
    
    /* Line Heights */
    --vendor-leading-none: 1;
    --vendor-leading-tight: 1.25;
    --vendor-leading-snug: 1.375;
    --vendor-leading-normal: 1.5;
    --vendor-leading-relaxed: 1.625;
    --vendor-leading-loose: 2;
    
    /* === Z-INDEX SCALE === */
    --vendor-z-dropdown: 1000;
    --vendor-z-sticky: 1020;
    --vendor-z-fixed: 1030;
    --vendor-z-modal-backdrop: 1040;
    --vendor-z-modal: 1050;
    --vendor-z-popover: 1060;
    --vendor-z-tooltip: 1070;
    --vendor-z-toast: 1080;
}

/* === LIGHT THEME === */
:root {
    --vendor-bg-primary: #f9fafb;
    --vendor-bg-secondary: var(--vendor-white);
    --vendor-bg-tertiary: var(--vendor-gray-100);
    --vendor-bg-accent: var(--vendor-brand-primary-50);
    
    --vendor-text-primary: var(--vendor-gray-900);
    --vendor-text-secondary: var(--vendor-gray-700);
    --vendor-text-tertiary: var(--vendor-gray-500);
    --vendor-text-inverse: var(--vendor-white);
    
    --vendor-border-primary: var(--vendor-gray-200);
    --vendor-border-secondary: var(--vendor-gray-300);
    --vendor-border-accent: var(--vendor-brand-primary-200);
    
    --vendor-surface-primary: var(--vendor-white);
    --vendor-surface-secondary: var(--vendor-gray-50);
    --vendor-surface-hover: var(--vendor-gray-100);
    --vendor-surface-active: var(--vendor-gray-200);
}

/* === DARK THEME === */
.dark {
    --vendor-bg-primary: #0f172a;
    --vendor-bg-secondary: #1e293b;
    --vendor-bg-tertiary: #334155;
    --vendor-bg-accent: #1e1b4b;
    
    --vendor-text-primary: #f1f5f9;
    --vendor-text-secondary: #cbd5e1;
    --vendor-text-tertiary: #94a3b8;
    --vendor-text-inverse: var(--vendor-gray-900);
    
    --vendor-border-primary: #334155;
    --vendor-border-secondary: #475569;
    --vendor-border-accent: #5b21b6;
    
    --vendor-surface-primary: #1e293b;
    --vendor-surface-secondary: #334155;
    --vendor-surface-hover: #475569;
    --vendor-surface-active: #64748b;
}

/* === GLOBAL RESET === */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    scroll-behavior: smooth;
}

body {
    font-family: var(--vendor-font-family-sans);
    font-size: var(--vendor-text-base);
    line-height: var(--vendor-leading-normal);
    color: var(--vendor-text-primary);
    background-color: var(--vendor-bg-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* === ACCESSIBILITY === */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Focus styles for accessibility */
:focus-visible {
    outline: 2px solid var(--vendor-brand-primary);
    outline-offset: 2px;
}

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
