/* ===== VENDOR LAYOUT CSS ===== */

/* === MAIN LAYOUT STRUCTURE === */
.vendor-body {
    font-family: var(--vendor-font-family-sans);
    background: var(--vendor-bg-primary);
    color: var(--vendor-text-primary);
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Loading Screen */
.vendor-loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--vendor-brand-primary), var(--vendor-brand-primary-700));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--vendor-z-modal);
    backdrop-filter: blur(10px);
}

.loading-content {
    text-align: center;
    color: var(--vendor-white);
    animation: fadeInUp 0.6s ease-out;
}

.loading-logo {
    margin-bottom: var(--vendor-space-8);
    animation: pulse 2s infinite;
}

.loading-logo img {
    height: 60px;
    width: auto;
    filter: brightness(0) invert(1);
}

.loading-logo-fallback {
    width: 60px;
    height: 60px;
    background: var(--vendor-white);
    color: var(--vendor-brand-primary);
    border-radius: var(--vendor-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-text-2xl);
    font-weight: var(--vendor-font-bold);
    margin: 0 auto;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--vendor-white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--vendor-space-4);
}

.loading-text {
    font-size: var(--vendor-text-lg);
    font-weight: var(--vendor-font-medium);
    opacity: 0.9;
}

/* Main App Container */
.vendor-app {
    display: flex;
    min-height: 100vh;
    background: var(--vendor-bg-primary);
}

/* Mobile Overlay */
.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: var(--vendor-z-modal-backdrop);
    backdrop-filter: blur(4px);
}

/* === SIDEBAR === */
.vendor-sidebar {
    width: var(--vendor-sidebar-width);
    background: var(--vendor-surface-primary);
    border-right: 1px solid var(--vendor-border-primary);
    display: flex;
    flex-direction: column;
    transition: var(--vendor-transition-base);
    z-index: var(--vendor-z-fixed);
    min-height: 100vh;
    position: relative;
    box-shadow: var(--vendor-shadow-sm);
}

.vendor-sidebar.collapsed {
    width: var(--vendor-sidebar-collapsed-width);
}

/* Mobile Sidebar */
@media (max-width: 1024px) {
    .vendor-sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        transform: translateX(-100%);
        z-index: var(--vendor-z-modal);
        box-shadow: var(--vendor-shadow-xl);
    }
    
    .vendor-sidebar.mobile-open {
        transform: translateX(0);
    }
}

/* Sidebar Header */
.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--vendor-space-6) var(--vendor-space-5);
    border-bottom: 1px solid var(--vendor-border-primary);
    background: var(--vendor-surface-primary);
    min-height: var(--vendor-header-height);
}

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    text-decoration: none;
    color: var(--vendor-text-primary);
    transition: var(--vendor-transition-fast);
    cursor: pointer;
}

.sidebar-brand:hover {
    transform: translateY(-1px);
}

.brand-logo {
    position: relative;
}

.brand-logo-img {
    height: 36px;
    width: auto;
    border-radius: var(--vendor-radius-md);
    transition: var(--vendor-transition-fast);
}

.brand-logo-fallback {
    width: 36px;
    height: 36px;
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    border-radius: var(--vendor-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-text-lg);
}

.brand-text {
    transition: var(--vendor-transition-fast);
}

.brand-title {
    font-size: var(--vendor-text-xl);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-brand-primary);
    white-space: nowrap;
    margin: 0;
    line-height: 1.2;
}

.brand-subtitle {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    margin: 0;
    line-height: 1;
}

.vendor-sidebar.collapsed .brand-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar-controls {
    display: flex;
    gap: var(--vendor-space-2);
}

.sidebar-toggle-btn,
.sidebar-close-btn {
    background: none;
    border: none;
    color: var(--vendor-text-secondary);
    font-size: var(--vendor-text-lg);
    cursor: pointer;
    padding: var(--vendor-space-2);
    border-radius: var(--vendor-radius-base);
    transition: var(--vendor-transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-toggle-btn:hover,
.sidebar-close-btn:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-primary);
}

.desktop-only {
    display: flex;
}

.mobile-only {
    display: none;
}

@media (max-width: 1024px) {
    .desktop-only {
        display: none;
    }

    .mobile-only {
        display: flex;
    }
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--vendor-text-secondary);
    font-size: var(--vendor-text-lg);
    cursor: pointer;
    padding: var(--vendor-space-2);
    border-radius: var(--vendor-radius-base);
    transition: var(--vendor-transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-toggle:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-primary);
}

/* Sidebar Navigation */
.sidebar-nav {
    flex: 1;
    padding: var(--vendor-space-4) 0;
    overflow-y: auto;
    overflow-x: hidden;
}

.nav-section {
    margin-bottom: var(--vendor-space-6);
}

.nav-section-title {
    font-size: var(--vendor-text-xs);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0 var(--vendor-space-5) var(--vendor-space-2);
    margin-bottom: var(--vendor-space-2);
    transition: var(--vendor-transition-fast);
}

.vendor-sidebar.collapsed .nav-section-title {
    opacity: 0;
    height: 0;
    padding: 0;
    margin: 0;
    overflow: hidden;
}

.nav-items {
    display: flex;
    flex-direction: column;
    gap: var(--vendor-space-1);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    color: var(--vendor-text-secondary);
    text-decoration: none;
    padding: var(--vendor-space-3) var(--vendor-space-5);
    margin: 0 var(--vendor-space-3);
    border-radius: var(--vendor-radius-md);
    transition: var(--vendor-transition-fast);
    position: relative;
    font-weight: var(--vendor-font-medium);
}

.nav-item:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-primary);
    transform: translateX(2px);
}

.nav-item.active {
    background: var(--vendor-bg-accent);
    color: var(--vendor-brand-primary);
    font-weight: var(--vendor-font-semibold);
}

.nav-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: var(--vendor-brand-primary);
    border-radius: 0 var(--vendor-radius-base) var(--vendor-radius-base) 0;
}

.nav-item-indicator {
    position: absolute;
    right: var(--vendor-space-3);
    width: 6px;
    height: 6px;
    background: var(--vendor-brand-primary);
    border-radius: 50%;
    opacity: 0;
    transition: var(--vendor-transition-fast);
}

.nav-item.active .nav-item-indicator {
    opacity: 1;
}

.nav-item-icon {
    font-size: var(--vendor-text-lg);
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.nav-item-text {
    white-space: nowrap;
    transition: var(--vendor-transition-fast);
}

.vendor-sidebar.collapsed .nav-item-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.nav-item-badge {
    background: var(--vendor-error);
    color: var(--vendor-white);
    font-size: var(--vendor-text-xs);
    font-weight: var(--vendor-font-semibold);
    padding: 2px 6px;
    border-radius: var(--vendor-radius-full);
    margin-left: auto;
    min-width: 18px;
    text-align: center;
    transition: var(--vendor-transition-fast);
}

.vendor-sidebar.collapsed .nav-item-badge {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: var(--vendor-space-4);
    border-top: 1px solid var(--vendor-border-primary);
    background: var(--vendor-surface-secondary);
}

.sidebar-user {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-3);
    border-radius: var(--vendor-radius-md);
    transition: var(--vendor-transition-fast);
    cursor: pointer;
}

.sidebar-user:hover {
    background: var(--vendor-surface-hover);
}

.sidebar-user-avatar {
    width: 36px;
    height: 36px;
    border-radius: var(--vendor-radius-full);
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--vendor-font-semibold);
    flex-shrink: 0;
}

.sidebar-user-info {
    flex: 1;
    min-width: 0;
    transition: var(--vendor-transition-fast);
}

.vendor-sidebar.collapsed .sidebar-user-info {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar-user-name {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-user-role {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* === ANIMATIONS === */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
